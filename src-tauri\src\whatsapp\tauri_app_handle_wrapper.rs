//! Tauri AppHandle wrapper for real Tauri integration

use async_trait::async_trait;
use serde_json;
use std::sync::Arc;
use tauri::{AppHandle, Emitter};
use whatsapp_ffi_client::WhatsAppError;

use crate::whatsapp::tauri_event_emitter::TauriAppHandle;

/// Wrapper for real Tauri AppHandle
#[derive(Debug, Clone)]
pub struct TauriAppHandleWrapper {
    app_handle: AppHandle,
}

impl TauriAppHandleWrapper {
    /// Create a new wrapper around a Tauri AppHandle
    pub fn new(app_handle: AppHandle) -> Self {
        Self { app_handle }
    }

    /// Get the wrapped AppHandle
    pub fn inner(&self) -> &AppHandle {
        &self.app_handle
    }
}

#[async_trait]
impl TauriAppHandle for TauriAppHandleWrapper {
    async fn emit(
        &self,
        event_name: &str,
        payload: &serde_json::Value,
    ) -> Result<(), WhatsAppError> {
        self.app_handle
            .emit(event_name, payload)
            .map_err(|e| WhatsAppError::Internal(format!("Failed to emit Tauri event: {}", e)))
    }
}

/// Create a TauriAppHandle trait object from a Tauri AppHandle
pub fn create_tauri_app_handle(app_handle: AppHandle) -> Arc<dyn TauriAppHandle> {
    Arc::new(TauriAppHandleWrapper::new(app_handle))
}
