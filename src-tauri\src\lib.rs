//! WhatsApp Sender Pro - Main application library.
//!
//! This is the main library crate for the WhatsApp Sender Pro desktop application.
//! It provides the core functionality for initializing and running the Tauri-based
//! desktop application with database services and state management.

#![deny(missing_docs)]

mod prelude;
mod state;
mod whatsapp;

use state::try_init_state;
use std::sync::Arc;
use tauri::{Manager, path::BaseDirectory};
use whatsapp::{
    commands::get_command_handlers,
    events::EventEmitter,
    service::WhatsAppService,
    state::ServiceConfig,
};

/// Gets the path to the embedded WhatsApp FFI DLL.
///
/// This function resolves the path to the `whatsapp_ffi.dll` file that is embedded
/// as a resource in the application bundle.
///
/// # Arguments
///
/// * `app_handle` - The Tauri application handle
///
/// # Returns
///
/// Returns the full path to the DLL file as a `String`.
///
/// # Errors
///
/// Returns an error if the resource path cannot be resolved.
fn get_whatsapp_ffi_dll_path(
    app_handle: &tauri::AppHandle,
) -> Result<String, Box<dyn std::error::Error>> {
    let resource_path = app_handle
        .path()
        .resolve("whatsapp_ffi.dll", BaseDirectory::Resource)?;

    Ok(resource_path.to_string_lossy().to_string())
}

/// Runs the main application.
///
/// This function initializes the application state, sets up the database services,
/// and starts the Tauri application with all necessary plugins and handlers.
///
/// # Panics
///
/// This function will panic if:
/// - The application state fails to initialize
/// - The Tauri application fails to start
/// - Database initialization fails
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub async fn run() {
    // Set up services
    let app_state = try_init_state()
        .await
        .expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .setup(|app| {
            tauri::async_runtime::block_on(async {
            // Initialize WhatsApp service
            let app_handle = app.handle().clone();

            // Get the path to the embedded DLL
            let dll_path = get_whatsapp_ffi_dll_path(&app_handle)
                .map_err(|e| format!("Failed to resolve DLL path: {}", e))?;

            tracing::info!("Using WhatsApp FFI DLL at: {}", dll_path);

            // Create WhatsApp service configuration with embedded DLL path
            let whatsapp_config = ServiceConfig::new(dll_path, "whatsapp.db".to_string());

            // Create event emitter
            let event_emitter = Arc::new(
                EventEmitter::new(app_handle.clone())
                    .await
                    .map_err(|e| format!("Failed to create event emitter: {}", e))?
            );

            // Create WhatsApp service
            let whatsapp_service = Arc::new(WhatsAppService::new(whatsapp_config, event_emitter));

            // Manage the WhatsApp service in Tauri state
            app.manage(whatsapp_service);

            Ok(())
            })
        })
        .invoke_handler(get_command_handlers())
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
