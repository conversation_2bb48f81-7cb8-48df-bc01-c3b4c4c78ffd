[workspace]
members = [".", "db/*", "crates/*"]
resolver = "2"

[package]
name = "app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.1", features = [] }

[dependencies]
chrono = { workspace = true }
dotenv = { workspace = true }
derive-getters = { workspace = true }
rust_decimal = { workspace = true }
sea-orm = { workspace = true }
sea-orm-migration = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tauri = { workspace = true, features = [] }
tauri-plugin-opener = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
typed-builder = { workspace = true }
url = { workspace = true }
uuid = { workspace = true }
validator = { workspace = true, features = ["derive"] }

app_config = { workspace = true }
db_entity = { workspace = true }
db_migration = { workspace = true }
db_service = { workspace = true }
whatsmeow_ffi = { workspace = true, features = ["tauri-integration"] }
async-trait.workspace = true

[workspace.dependencies]
argon2 = "0.5.3"
async-recursion = "1.1.1"
async-trait = "0.1.88"
chrono = "0.4.41"
config = "0.15.13"
derive_more = "2.0.1"
derive-getters = "0.5.0"
dotenv = "0.15.0"
jsonwebtoken = "9.3.1"
pretty_assertions = "1.4.1"
rust_decimal = "1.37.2"
sea-orm = "1.1.14"
sea-orm-migration = "1.1.14"
serde = "1.0.219"
serde_json = "1.0.142"
thiserror = "2.0.12"
tokio = "1.47.1"
toml = "0.9.5"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
typed-builder = "0.21.0"
url = "2.5.4"
uuid = "1.18.0"
validator = "0.20.0"
tauri = "2.7.0"
tauri-plugin-opener = "2.4.0"

app_config = { path = "crates/config" }
db_entity = { path = "db/entity" }
db_migration = { path = "db/migration" }
db_service = { path = "db/service" }
whatsmeow_ffi = { path = "crates/whatsmeow_ffi" }
