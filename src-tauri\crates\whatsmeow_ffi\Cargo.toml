[package]
name = "whatsmeow_ffi"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.47.1", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.142"
thiserror = "2.0.12"
async-trait = "0.1.88"
libloading = "0.8.8"
tracing = "0.1.41"
tracing-subscriber = "0.3.19"
chrono = { version = "0.4.41", features = ["serde"] }
qrcode = "0.14.1"
colored = "3.0.0"
base64 = "0.22.1"
mime_guess = "2.0.5"
rand = "0.8.5"
tauri = { version = "2.7.0", optional = true }

[features]
default = []
tauri-integration = ["tauri"]

[dev-dependencies]
tokio-test = "0.4.4"
tempfile = "3.8.1"
futures = "0.3.30"

[lib]
name = "whatsapp_ffi_client"
crate-type = ["lib"]
