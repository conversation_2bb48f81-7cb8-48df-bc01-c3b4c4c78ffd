//! Tauri Event Emitter for real-time event emission to frontend
//!
//! This module provides a comprehensive event emission system for sending WhatsApp events
//! to the Tauri frontend with features like batching, throttling, filtering, and subscription management.

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use whatsapp_ffi_client::WhatsAppEvent;
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, RwLock as TokioRwLock};
use tracing::{debug, error, info, warn};

/// Unique identifier for event subscriptions
pub type SubscriptionId = String;

/// Event filter for selective event processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventFilter {
    /// Match all events
    All,
    /// Match events by type
    ByType(TauriEventType),
    /// Match events by multiple types
    ByTypes(Vec<TauriEventType>),
    /// Match events by custom predicate
    Custom {
        name: String,
        parameters: HashMap<String, serde_json::Value>,
    },
    /// Combine filters with AND logic
    And(Vec<EventFilter>),
    /// Combine filters with OR logic
    Or(Vec<EventFilter>),
    /// Negate a filter
    Not(Box<EventFilter>),
}

/// Event types for Tauri frontend
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TauriEventType {
    QRCode,
    MessageReceived,
    MessageSent,
    ConnectionStatusChanged,
    ServiceStatusChanged,
    ServiceError,
    BulkOperationProgress,
    ContactPresenceUpdate,
    AuthenticationRequired,
    Custom(String),
}

impl From<&WhatsAppEvent> for TauriEventType {
    fn from(event: &WhatsAppEvent) -> Self {
        match event {
            WhatsAppEvent::QRCode(_) => TauriEventType::QRCode,
            WhatsAppEvent::MessageReceived(_) => TauriEventType::MessageReceived,
            WhatsAppEvent::ConnectionStatusChanged { .. } => TauriEventType::ConnectionStatusChanged,
            WhatsAppEvent::Error { .. } => TauriEventType::ServiceError,
        }
    }
}

/// Serializable event for Tauri frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TauriEvent {
    pub id: String,
    pub event_type: TauriEventType,
    pub payload: serde_json::Value,
    pub timestamp: DateTime<Utc>,
    pub metadata: EventMetadata,
}

/// Event metadata for tracking and debugging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMetadata {
    pub subscription_id: Option<String>,
    pub batch_id: Option<String>,
    pub sequence_number: u64,
    pub retry_count: u32,
    pub processing_time_ms: Option<u64>,
}

/// Event subscription configuration
#[derive(Debug, Clone)]
pub struct EventSubscription {
    pub id: SubscriptionId,
    pub filter: EventFilter,
    pub created_at: DateTime<Utc>,
    pub last_event_at: Option<DateTime<Utc>>,
    pub event_count: u64,
    pub is_active: bool,
}

/// Event batching configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConfig {
    /// Maximum number of events per batch
    pub max_batch_size: usize,
    /// Maximum time to wait before sending a batch (milliseconds)
    pub max_batch_timeout_ms: u64,
    /// Enable batching
    pub enabled: bool,
}

impl Default for BatchConfig {
    fn default() -> Self {
        Self {
            max_batch_size: 10,
            max_batch_timeout_ms: 100,
            enabled: false,
        }
    }
}

/// Event throttling configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThrottleConfig {
    /// Maximum events per second
    pub max_events_per_second: u32,
    /// Time window for rate limiting (milliseconds)
    pub time_window_ms: u64,
    /// Enable throttling
    pub enabled: bool,
}

impl Default for ThrottleConfig {
    fn default() -> Self {
        Self {
            max_events_per_second: 100,
            time_window_ms: 1000,
            enabled: false,
        }
    }
}

/// Configuration for TauriEventEmitter
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TauriEventEmitterConfig {
    /// Event batching configuration
    pub batch_config: BatchConfig,
    /// Event throttling configuration
    pub throttle_config: ThrottleConfig,
    /// Maximum number of queued events
    pub max_queue_size: usize,
    /// Enable event persistence for offline scenarios
    pub enable_persistence: bool,
    /// Event cleanup interval in seconds
    pub cleanup_interval_seconds: u64,
}

impl Default for TauriEventEmitterConfig {
    fn default() -> Self {
        Self {
            batch_config: BatchConfig::default(),
            throttle_config: ThrottleConfig::default(),
            max_queue_size: 1000,
            enable_persistence: false,
            cleanup_interval_seconds: 3600,
        }
    }
}

/// Event batch for batched emission
#[derive(Debug, Clone)]
struct EventBatch {
    pub id: String,
    pub events: Vec<TauriEvent>,
    pub created_at: Instant,
}

/// Rate limiter for event throttling
#[derive(Debug)]
struct RateLimiter {
    config: ThrottleConfig,
    event_timestamps: VecDeque<Instant>,
}

impl RateLimiter {
    fn new(config: ThrottleConfig) -> Self {
        Self {
            config,
            event_timestamps: VecDeque::new(),
        }
    }

    fn can_emit(&mut self) -> bool {
        if !self.config.enabled {
            return true;
        }

        let now = Instant::now();
        let window_duration = Duration::from_millis(self.config.time_window_ms);

        // Remove old timestamps outside the window
        while let Some(&front_time) = self.event_timestamps.front() {
            if now.duration_since(front_time) > window_duration {
                self.event_timestamps.pop_front();
            } else {
                break;
            }
        }

        // Check if we're under the rate limit
        if self.event_timestamps.len() < self.config.max_events_per_second as usize {
            self.event_timestamps.push_back(now);
            true
        } else {
            false
        }
    }

    fn time_until_next_slot(&self) -> Option<Duration> {
        if !self.config.enabled {
            return None;
        }

        if let Some(&front_time) = self.event_timestamps.front() {
            let window_duration = Duration::from_millis(self.config.time_window_ms);
            let elapsed = Instant::now().duration_since(front_time);
            if elapsed < window_duration {
                Some(window_duration - elapsed)
            } else {
                None
            }
        } else {
            None
        }
    }
}

/// Statistics for TauriEventEmitter
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct TauriEventEmitterStats {
    pub total_events_emitted: u64,
    pub events_by_type: HashMap<TauriEventType, u64>,
    pub active_subscriptions: usize,
    pub total_subscriptions_created: u64,
    pub batches_sent: u64,
    pub events_throttled: u64,
    pub events_dropped: u64,
    pub average_batch_size: f64,
    pub average_processing_time_ms: f64,
    pub last_event_timestamp: Option<DateTime<Utc>>,
}

/// Mock Tauri app handle for testing
#[derive(Debug, Clone)]
pub struct MockTauriAppHandle {
    pub emitted_events: Arc<Mutex<Vec<(String, serde_json::Value)>>>,
}

impl MockTauriAppHandle {
    pub fn new() -> Self {
        Self {
            emitted_events: Arc::new(Mutex::new(Vec::new())),
        }
    }

    pub async fn emit(&self, event_name: &str, payload: &serde_json::Value) -> Result<()> {
        let mut events = self.emitted_events.lock().await;
        events.push((event_name.to_string(), payload.clone()));
        Ok(())
    }

    pub async fn get_emitted_events(&self) -> Vec<(String, serde_json::Value)> {
        self.emitted_events.lock().await.clone()
    }

    pub async fn clear_events(&self) {
        self.emitted_events.lock().await.clear();
    }
}

/// Trait for Tauri app handle abstraction
#[async_trait]
pub trait TauriAppHandle: Send + Sync {
    async fn emit(&self, event_name: &str, payload: &serde_json::Value) -> Result<()>;
}

#[async_trait]
impl TauriAppHandle for MockTauriAppHandle {
    async fn emit(&self, event_name: &str, payload: &serde_json::Value) -> Result<()> {
        self.emit(event_name, payload).await
    }
}

/// Main TauriEventEmitter implementation
pub struct TauriEventEmitter {
    config: TauriEventEmitterConfig,
    app_handle: Arc<dyn TauriAppHandle>,
    subscriptions: Arc<TokioRwLock<HashMap<SubscriptionId, EventSubscription>>>,
    event_queue: Arc<Mutex<VecDeque<TauriEvent>>>,
    current_batch: Arc<Mutex<Option<EventBatch>>>,
    rate_limiter: Arc<Mutex<RateLimiter>>,
    stats: Arc<RwLock<TauriEventEmitterStats>>,
    sequence_counter: Arc<Mutex<u64>>,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl TauriEventEmitter {
    /// Create a new TauriEventEmitter
    pub fn new(
        config: TauriEventEmitterConfig,
        app_handle: Arc<dyn TauriAppHandle>,
    ) -> Self {
        let rate_limiter = RateLimiter::new(config.throttle_config.clone());

        Self {
            config: config.clone(),
            app_handle,
            subscriptions: Arc::new(TokioRwLock::new(HashMap::new())),
            event_queue: Arc::new(Mutex::new(VecDeque::new())),
            current_batch: Arc::new(Mutex::new(None)),
            rate_limiter: Arc::new(Mutex::new(rate_limiter)),
            stats: Arc::new(RwLock::new(TauriEventEmitterStats::default())),
            sequence_counter: Arc::new(Mutex::new(0)),
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
        }
    }

    /// Start the event processing loop
    pub async fn start(&self) -> Result<()> {
        info!("Starting TauriEventEmitter");

        // Start event processing task
        self.start_event_processing_task().await;

        // Start batch processing task if batching is enabled
        if self.config.batch_config.enabled {
            self.start_batch_processing_task().await;
        }

        // Start cleanup task if persistence is enabled
        if self.config.enable_persistence {
            self.start_cleanup_task().await;
        }

        Ok(())
    }

    /// Emit a WhatsApp event to the frontend
    pub async fn emit_event(&self, event: WhatsAppEvent) -> Result<()> {
        let start_time = Instant::now();

        // Convert to TauriEvent
        let tauri_event = self.convert_to_tauri_event(event).await?;

        // Check if any subscriptions match this event
        let matching_subscriptions = self.get_matching_subscriptions(&tauri_event).await;

        if matching_subscriptions.is_empty() {
            debug!("No subscriptions match event type {:?}", tauri_event.event_type);
            return Ok(());
        }

        // Check rate limiting
        {
            let mut rate_limiter = self.rate_limiter.lock().await;
            if !rate_limiter.can_emit() {
                warn!("Event throttled due to rate limiting");
                self.update_stats_throttled().await;
                return Ok(());
            }
        }

        // Add to queue
        {
            let mut queue = self.event_queue.lock().await;
            if queue.len() >= self.config.max_queue_size {
                warn!("Event queue full, dropping oldest event");
                queue.pop_front();
                self.update_stats_dropped().await;
            }
            queue.push_back(tauri_event.clone());
        }

        // Update processing time stats
        let processing_time = start_time.elapsed().as_millis() as u64;
        self.update_stats_processing_time(processing_time).await;

        debug!(
            event_type = ?tauri_event.event_type,
            event_id = %tauri_event.id,
            "Event queued for emission"
        );

        Ok(())
    }

    /// Subscribe to events with a filter
    pub async fn subscribe(&self, filter: EventFilter) -> Result<SubscriptionId> {
        let subscription_id = self.generate_subscription_id().await;

        let subscription = EventSubscription {
            id: subscription_id.clone(),
            filter,
            created_at: Utc::now(),
            last_event_at: None,
            event_count: 0,
            is_active: true,
        };

        {
            let mut subscriptions = self.subscriptions.write().await;
            subscriptions.insert(subscription_id.clone(), subscription);

            let mut stats = self.stats.write().unwrap();
            stats.active_subscriptions = subscriptions.len();
            stats.total_subscriptions_created += 1;
        }

        info!(subscription_id = %subscription_id, "Created event subscription");
        Ok(subscription_id)
    }

    /// Unsubscribe from events
    pub async fn unsubscribe(&self, subscription_id: &SubscriptionId) -> Result<()> {
        let mut subscriptions = self.subscriptions.write().await;
        if subscriptions.remove(subscription_id).is_some() {
            let mut stats = self.stats.write().unwrap();
            stats.active_subscriptions = subscriptions.len();

            info!(subscription_id = %subscription_id, "Removed event subscription");
            Ok(())
        } else {
            Err(WhatsAppError::Internal(format!(
                "Subscription not found: {}",
                subscription_id
            )))
        }
    }

    /// Get current statistics
    pub fn get_stats(&self) -> TauriEventEmitterStats {
        let stats = self.stats.read().unwrap();
        let subscriptions = self.subscriptions.try_read().map(|s| s.len()).unwrap_or(0);

        let mut stats_clone = stats.clone();
        stats_clone.active_subscriptions = subscriptions;
        stats_clone
    }

    /// Update configuration
    pub async fn update_config(&self, new_config: TauriEventEmitterConfig) -> Result<()> {
        // Update rate limiter if throttle config changed
        if new_config.throttle_config.enabled != self.config.throttle_config.enabled
            || new_config.throttle_config.max_events_per_second != self.config.throttle_config.max_events_per_second
            || new_config.throttle_config.time_window_ms != self.config.throttle_config.time_window_ms
        {
            let mut rate_limiter = self.rate_limiter.lock().await;
            *rate_limiter = RateLimiter::new(new_config.throttle_config.clone());
        }

        // Note: In a real implementation, we would need to handle config updates more carefully
        // For now, we just log the update
        info!("TauriEventEmitter configuration updated");
        Ok(())
    }

    /// Shutdown the event emitter
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down TauriEventEmitter");
        self.shutdown_signal.notify_waiters();
        Ok(())
    }

    // Private helper methods

    async fn convert_to_tauri_event(&self, event: WhatsAppEvent) -> Result<TauriEvent> {
        let event_type = TauriEventType::from(&event);
        let payload = serde_json::to_value(&event)
            .map_err(|e| WhatsAppError::Internal(format!("Failed to serialize event: {}", e)))?;

        let sequence_number = {
            let mut counter = self.sequence_counter.lock().await;
            *counter += 1;
            *counter
        };

        Ok(TauriEvent {
            id: self.generate_event_id().await,
            event_type,
            payload,
            timestamp: Utc::now(),
            metadata: EventMetadata {
                subscription_id: None,
                batch_id: None,
                sequence_number,
                retry_count: 0,
                processing_time_ms: None,
            },
        })
    }

    async fn get_matching_subscriptions(&self, event: &TauriEvent) -> Vec<SubscriptionId> {
        let subscriptions = self.subscriptions.read().await;
        subscriptions
            .values()
            .filter(|sub| sub.is_active && self.matches_filter(event, &sub.filter))
            .map(|sub| sub.id.clone())
            .collect()
    }

    fn matches_filter(&self, event: &TauriEvent, filter: &EventFilter) -> bool {
        match filter {
            EventFilter::All => true,
            EventFilter::ByType(event_type) => &event.event_type == event_type,
            EventFilter::ByTypes(types) => types.contains(&event.event_type),
            EventFilter::Custom { .. } => {
                // Custom filter evaluation would go here
                true
            }
            EventFilter::And(filters) => filters.iter().all(|f| self.matches_filter(event, f)),
            EventFilter::Or(filters) => filters.iter().any(|f| self.matches_filter(event, f)),
            EventFilter::Not(filter) => !self.matches_filter(event, filter),
        }
    }

    async fn start_event_processing_task(&self) {
        let event_queue = self.event_queue.clone();
        let app_handle = self.app_handle.clone();
        let config = self.config.clone();
        let stats = self.stats.clone();
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_millis(10));

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let events_to_process = {
                            let mut queue = event_queue.lock().await;
                            let batch_size = if config.batch_config.enabled {
                                config.batch_config.max_batch_size
                            } else {
                                1
                            };
                            
                            let mut events = Vec::new();
                            for _ in 0..batch_size {
                                if let Some(event) = queue.pop_front() {
                                    events.push(event);
                                } else {
                                    break;
                                }
                            }
                            events
                        };

                        if !events_to_process.is_empty() {
                            if config.batch_config.enabled && events_to_process.len() > 1 {
                                // Send as batch
                                if let Err(e) = Self::emit_batch(&app_handle, &events_to_process).await {
                                    error!("Failed to emit event batch: {}", e);
                                } else {
                                    Self::update_batch_stats(&stats, events_to_process.len()).await;
                                }
                            } else {
                                // Send individual events
                                for event in events_to_process {
                                    if let Err(e) = Self::emit_single_event(&app_handle, &event).await {
                                        error!("Failed to emit event: {}", e);
                                    } else {
                                        Self::update_event_stats(&stats, &event).await;
                                    }
                                }
                            }
                        }
                    }
                    _ = shutdown_signal.notified() => {
                        debug!("Event processing task shutting down");
                        break;
                    }
                }
            }
        });
    }

    async fn start_batch_processing_task(&self) {
        let current_batch = self.current_batch.clone();
        let app_handle = self.app_handle.clone();
        let config = self.config.clone();
        let stats = self.stats.clone();
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_millis(
                config.batch_config.max_batch_timeout_ms,
            ));

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let batch_to_send = {
                            let mut batch_guard = current_batch.lock().await;
                            if let Some(batch) = batch_guard.take() {
                                if !batch.events.is_empty() {
                                    Some(batch)
                                } else {
                                    None
                                }
                            } else {
                                None
                            }
                        };

                        if let Some(batch) = batch_to_send {
                            if let Err(e) = Self::emit_batch(&app_handle, &batch.events).await {
                                error!("Failed to emit batch: {}", e);
                            } else {
                                Self::update_batch_stats(&stats, batch.events.len()).await;
                            }
                        }
                    }
                    _ = shutdown_signal.notified() => {
                        debug!("Batch processing task shutting down");
                        break;
                    }
                }
            }
        });
    }

    async fn start_cleanup_task(&self) {
        let cleanup_interval = self.config.cleanup_interval_seconds;
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(cleanup_interval));

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        // Cleanup logic would go here
                        debug!("Performing event cleanup");
                    }
                    _ = shutdown_signal.notified() => {
                        debug!("Cleanup task shutting down");
                        break;
                    }
                }
            }
        });
    }

    async fn emit_single_event(
        app_handle: &Arc<dyn TauriAppHandle>,
        event: &TauriEvent,
    ) -> Result<()> {
        let event_name = Self::get_event_name(&event.event_type);
        app_handle.emit(&event_name, &event.payload).await
    }

    async fn emit_batch(
        app_handle: &Arc<dyn TauriAppHandle>,
        events: &[TauriEvent],
    ) -> Result<()> {
        let batch_payload = serde_json::json!({
            "batch_id": Self::generate_batch_id(),
            "events": events,
            "timestamp": Utc::now()
        });

        app_handle.emit("whatsapp:event-batch", &batch_payload).await
    }

    fn get_event_name(event_type: &TauriEventType) -> String {
        match event_type {
            TauriEventType::QRCode => "whatsapp:qr-code".to_string(),
            TauriEventType::MessageReceived => "whatsapp:message-received".to_string(),
            TauriEventType::MessageSent => "whatsapp:message-sent".to_string(),
            TauriEventType::ConnectionStatusChanged => "whatsapp:connection-status-changed".to_string(),
            TauriEventType::ServiceStatusChanged => "whatsapp:service-status-changed".to_string(),
            TauriEventType::ServiceError => "whatsapp:service-error".to_string(),
            TauriEventType::BulkOperationProgress => "whatsapp:bulk-operation-progress".to_string(),
            TauriEventType::ContactPresenceUpdate => "whatsapp:contact-presence-update".to_string(),
            TauriEventType::AuthenticationRequired => "whatsapp:authentication-required".to_string(),
            TauriEventType::Custom(name) => format!("whatsapp:custom:{}", name),
        }
    }

    async fn update_event_stats(stats: &Arc<RwLock<TauriEventEmitterStats>>, event: &TauriEvent) {
        let mut stats_guard = stats.write().unwrap();
        stats_guard.total_events_emitted += 1;
        *stats_guard
            .events_by_type
            .entry(event.event_type.clone())
            .or_insert(0) += 1;
        stats_guard.last_event_timestamp = Some(event.timestamp);
    }

    async fn update_batch_stats(stats: &Arc<RwLock<TauriEventEmitterStats>>, batch_size: usize) {
        let mut stats_guard = stats.write().unwrap();
        stats_guard.batches_sent += 1;
        stats_guard.average_batch_size = 
            (stats_guard.average_batch_size + batch_size as f64) / 2.0;
    }

    async fn update_stats_throttled(&self) {
        let mut stats = self.stats.write().unwrap();
        stats.events_throttled += 1;
    }

    async fn update_stats_dropped(&self) {
        let mut stats = self.stats.write().unwrap();
        stats.events_dropped += 1;
    }

    async fn update_stats_processing_time(&self, processing_time_ms: u64) {
        let mut stats = self.stats.write().unwrap();
        stats.average_processing_time_ms = 
            (stats.average_processing_time_ms + processing_time_ms as f64) / 2.0;
    }

    async fn generate_event_id(&self) -> String {
        format!("evt_{}", Utc::now().timestamp_millis())
    }

    async fn generate_subscription_id(&self) -> String {
        format!("sub_{}", Utc::now().timestamp_millis())
    }

    fn generate_batch_id() -> String {
        format!("batch_{}", Utc::now().timestamp_millis())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::entities::Message;

    #[tokio::test]
    async fn test_tauri_event_emitter_creation() {
        let config = TauriEventEmitterConfig::default();
        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle);

        let stats = emitter.get_stats();
        assert_eq!(stats.total_events_emitted, 0);
        assert_eq!(stats.active_subscriptions, 0);
    }

    #[tokio::test]
    async fn test_event_subscription_and_unsubscription() {
        let config = TauriEventEmitterConfig::default();
        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle);

        // Subscribe to all events
        let subscription_id = emitter.subscribe(EventFilter::All).await.unwrap();
        assert!(!subscription_id.is_empty());

        let stats = emitter.get_stats();
        assert_eq!(stats.active_subscriptions, 1);
        assert_eq!(stats.total_subscriptions_created, 1);

        // Unsubscribe
        emitter.unsubscribe(&subscription_id).await.unwrap();

        let stats = emitter.get_stats();
        assert_eq!(stats.active_subscriptions, 0);
    }

    #[tokio::test]
    async fn test_event_filtering() {
        let config = TauriEventEmitterConfig::default();
        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle.clone());

        // Subscribe to QR code events only
        let _subscription_id = emitter
            .subscribe(EventFilter::ByType(TauriEventType::QRCode))
            .await
            .unwrap();

        emitter.start().await.unwrap();

        // Emit QR code event
        let qr_event = WhatsAppEvent::QRCode("test_qr_code".to_string());
        emitter.emit_event(qr_event).await.unwrap();

        // Emit message event (should be filtered out)
        let message = Message::new_text(
            "test_sender".to_string(),
            "test_message".to_string(),
            1234567890,
            "msg_123".to_string(),
        );
        let message_event = WhatsAppEvent::MessageReceived(Box::new(message));
        emitter.emit_event(message_event).await.unwrap();

        // Give some time for processing
        tokio::time::sleep(Duration::from_millis(50)).await;

        // Check emitted events
        let emitted_events = app_handle.get_emitted_events().await;
        
        // Should only have QR code event
        assert_eq!(emitted_events.len(), 1);
        assert_eq!(emitted_events[0].0, "whatsapp:qr-code");
    }

    #[tokio::test]
    async fn test_event_batching() {
        let mut config = TauriEventEmitterConfig::default();
        config.batch_config.enabled = true;
        config.batch_config.max_batch_size = 3;
        config.batch_config.max_batch_timeout_ms = 100;

        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle.clone());

        let _subscription_id = emitter.subscribe(EventFilter::All).await.unwrap();
        emitter.start().await.unwrap();

        // Emit multiple events
        for i in 0..5 {
            let qr_event = WhatsAppEvent::QRCode(format!("qr_code_{}", i));
            emitter.emit_event(qr_event).await.unwrap();
        }

        // Give time for batch processing
        tokio::time::sleep(Duration::from_millis(200)).await;

        let stats = emitter.get_stats();
        assert!(stats.batches_sent > 0);
    }

    #[tokio::test]
    async fn test_event_throttling() {
        let mut config = TauriEventEmitterConfig::default();
        config.throttle_config.enabled = true;
        config.throttle_config.max_events_per_second = 2;
        config.throttle_config.time_window_ms = 1000;

        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle.clone());

        let _subscription_id = emitter.subscribe(EventFilter::All).await.unwrap();
        emitter.start().await.unwrap();

        // Emit more events than the rate limit allows
        for i in 0..5 {
            let qr_event = WhatsAppEvent::QRCode(format!("qr_code_{}", i));
            emitter.emit_event(qr_event).await.unwrap();
        }

        // Give time for processing
        tokio::time::sleep(Duration::from_millis(100)).await;

        let stats = emitter.get_stats();
        assert!(stats.events_throttled > 0);
    }

    #[tokio::test]
    async fn test_rate_limiter() {
        let config = ThrottleConfig {
            enabled: true,
            max_events_per_second: 2,
            time_window_ms: 1000,
        };

        let mut rate_limiter = RateLimiter::new(config);

        // Should allow first 2 events
        assert!(rate_limiter.can_emit());
        assert!(rate_limiter.can_emit());

        // Should throttle the 3rd event
        assert!(!rate_limiter.can_emit());

        // Check time until next slot
        let time_until_next = rate_limiter.time_until_next_slot();
        assert!(time_until_next.is_some());
    }

    #[tokio::test]
    async fn test_event_serialization() {
        let config = TauriEventEmitterConfig::default();
        let app_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = TauriEventEmitter::new(config, app_handle);

        // Test QR code event serialization
        let qr_event = WhatsAppEvent::QRCode("test_qr_code".to_string());
        let tauri_event = emitter.convert_to_tauri_event(qr_event).await.unwrap();

        assert_eq!(tauri_event.event_type, TauriEventType::QRCode);
        assert!(tauri_event.payload.is_object()); // QR code event is serialized as an object

        // Test message event serialization
        let message = Message::new_text(
            "test_sender".to_string(),
            "test_message".to_string(),
            1234567890,
            "msg_123".to_string(),
        );
        let message_event = WhatsAppEvent::MessageReceived(Box::new(message));
        let tauri_event = emitter.convert_to_tauri_event(message_event).await.unwrap();

        assert_eq!(tauri_event.event_type, TauriEventType::MessageReceived);
        assert!(tauri_event.payload.is_object());
    }

    #[tokio::test]
    async fn test_mock_tauri_app_handle() {
        let app_handle = MockTauriAppHandle::new();

        let payload = serde_json::json!({"test": "data"});
        app_handle.emit("test_event", &payload).await.unwrap();

        let events = app_handle.get_emitted_events().await;
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].0, "test_event");
        assert_eq!(events[0].1, payload);

        app_handle.clear_events().await;
        let events = app_handle.get_emitted_events().await;
        assert_eq!(events.len(), 0);
    }
}