//! Event emission for Tauri frontend integration

use std::sync::Arc;
use tauri::AppHandle;
use tracing::{error, info};

use whatsapp_ffi_client::{ConnectionStatus, Message, WhatsAppEvent};

use crate::whatsapp::{
    tauri_app_handle_wrapper::create_tauri_app_handle,
    tauri_event_emitter::{
        EventFilter, MockTauriAppHandle, TauriEventEmitter, TauriEventEmitterConfig,
        TauriEventEmitterStats,
    },
};

use super::state::ServiceStatus;

/// Enhanced event emitter using the new TauriEventEmitter system
pub struct EventEmitter {
    emitter: Arc<TauriEventEmitter>,
}

impl EventEmitter {
    /// Create a new enhanced event emitter
    pub async fn new(app_handle: AppHandle) -> Result<Self, Box<dyn std::error::Error>> {
        let config = TauriEventEmitterConfig::default();
        let tauri_handle = create_tauri_app_handle(app_handle);
        let emitter = Arc::new(TauriEventEmitter::new(config, tauri_handle));

        // Start the event emitter
        emitter.start().await?;

        // Subscribe to all events by default
        emitter.subscribe(EventFilter::All).await?;

        Ok(EventEmitter { emitter })
    }

    /// Create a mock event emitter for testing
    pub async fn new_mock() -> Result<Self, Box<dyn std::error::Error>> {
        let config = TauriEventEmitterConfig::default();
        let mock_handle = Arc::new(MockTauriAppHandle::new());
        let emitter = Arc::new(TauriEventEmitter::new(config, mock_handle));

        // Start the event emitter
        emitter.start().await?;

        // Subscribe to all events by default
        emitter.subscribe(EventFilter::All).await?;

        Ok(EventEmitter { emitter })
    }

    /// Emit QR code event
    pub async fn emit_qr_code(&self, qr_code: &str) {
        let event = WhatsAppEvent::QRCode(qr_code.to_string());
        if let Err(e) = self.emitter.emit_event(event).await {
            error!("Failed to emit QR code event: {}", e);
        }
    }

    /// Emit message received event
    pub async fn emit_message_received(&self, message: &Message) {
        let event = WhatsAppEvent::MessageReceived(Box::new(message.clone()));
        if let Err(e) = self.emitter.emit_event(event).await {
            error!("Failed to emit message received event: {}", e);
        }
    }

    /// Emit message sent event
    pub async fn emit_message_sent(&self, to: &str, message: &str, message_id: &str) {
        // For message sent events, we'll log for now since WhatsAppEvent doesn't have MessageSent variant
        info!("Message sent to {}: {} (ID: {})", to, message, message_id);

        // We could extend WhatsAppEvent to include MessageSent if needed
        // For now, we'll emit a custom event directly through the emitter
    }

    /// Emit connection status changed event
    pub async fn emit_connection_status_changed(
        &self,
        status: ConnectionStatus,
        reason: Option<String>,
    ) {
        let event = WhatsAppEvent::ConnectionStatusChanged { status, reason };
        if let Err(e) = self.emitter.emit_event(event).await {
            error!("Failed to emit connection status changed event: {}", e);
        }
    }

    /// Emit service status changed event
    pub async fn emit_service_status_changed(&self, status: ServiceStatus) {
        // Service status events would need custom handling since WhatsAppEvent doesn't have this variant
        info!("Service status changed to: {:?}", status);

        // We could extend WhatsAppEvent to include ServiceStatusChanged if needed
    }

    /// Emit service error event
    pub async fn emit_service_error(&self, error: &str) {
        let event = WhatsAppEvent::Error {
            code: 500, // Generic error code
            message: error.to_string(),
        };
        if let Err(e) = self.emitter.emit_event(event).await {
            error!("Failed to emit service error event: {}", e);
        }
    }

    /// Get the underlying TauriEventEmitter for advanced operations
    pub fn get_emitter(&self) -> &Arc<TauriEventEmitter> {
        &self.emitter
    }

    /// Subscribe to specific event types
    pub async fn subscribe(
        &self,
        filter: EventFilter,
    ) -> Result<String, Box<dyn std::error::Error>> {
        Ok(self.emitter.subscribe(filter).await?)
    }

    /// Unsubscribe from events
    pub async fn unsubscribe(
        &self,
        subscription_id: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        Ok(self
            .emitter
            .unsubscribe(&subscription_id.to_string())
            .await?)
    }

    /// Get event emitter statistics
    pub fn get_stats(&self) -> TauriEventEmitterStats {
        self.emitter.get_stats()
    }

    /// Shutdown the event emitter
    pub async fn shutdown(&self) -> Result<(), Box<dyn std::error::Error>> {
        Ok(self.emitter.shutdown().await?)
    }
}
