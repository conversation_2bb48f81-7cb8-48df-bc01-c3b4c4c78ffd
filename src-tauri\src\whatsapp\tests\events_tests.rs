//! Unit tests for event emission and handling

use std::sync::Arc;
use whatsapp_ffi_client::{ConnectionStatus, Message};

use crate::whatsapp::{events::EventEmitter, state::ServiceStatus};

#[tokio::test]
async fn test_event_emitter_creation() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    let stats = emitter.get_stats();
    assert_eq!(stats.total_events_emitted, 0);
}

#[tokio::test]
async fn test_event_emitter_qr_code() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    emitter.emit_qr_code("test_qr_code").await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_event_emitter_message_received() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    let message = Message::new_text(
        "test_sender".to_string(),
        "test_message".to_string(),
        1234567890,
        "msg_123".to_string(),
    );

    emitter.emit_message_received(&message).await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_event_emitter_message_sent() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    emitter
        .emit_message_sent("test_recipient", "test_message", "msg_123")
        .await;

    // Message sent events are currently just logged, so no stats change expected
    let stats = emitter.get_stats();
    // Since message sent doesn't emit a WhatsAppEvent, stats won't change
    assert_eq!(stats.total_events_emitted, 0);
}

#[tokio::test]
async fn test_event_emitter_connection_status_changed() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    emitter
        .emit_connection_status_changed(
            ConnectionStatus::Connected,
            Some("Test connection".to_string()),
        )
        .await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_event_emitter_service_status_changed() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    emitter
        .emit_service_status_changed(ServiceStatus::Connected)
        .await;

    // Service status events are currently just logged, so no stats change expected
    let stats = emitter.get_stats();
    // Since service status doesn't emit a WhatsAppEvent, stats won't change
    assert_eq!(stats.total_events_emitted, 0);
}

#[tokio::test]
async fn test_event_emitter_service_error() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    emitter.emit_service_error("Test error").await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_event_emitter_multiple_events() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    // Emit multiple events
    emitter.emit_qr_code("qr_1").await;
    emitter.emit_qr_code("qr_2").await;
    emitter.emit_service_error("error_1").await;

    let message = Message::new_text(
        "sender".to_string(),
        "message".to_string(),
        1234567890,
        "msg_1".to_string(),
    );
    emitter.emit_message_received(&message).await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted >= 4); // At least 4 events should be emitted
}

#[tokio::test]
async fn test_event_emitter_concurrent_access() {
    let emitter = Arc::new(EventEmitter::new_mock().await.unwrap());

    let mut handles = vec![];

    // Spawn multiple tasks that emit events concurrently
    for i in 0..5 {
        let emitter_clone = Arc::clone(&emitter);
        let handle = tokio::spawn(async move {
            emitter_clone.emit_qr_code(&format!("qr_{}", i)).await;
        });
        handles.push(handle);
    }

    // Wait for all tasks to complete
    for handle in handles {
        handle.await.unwrap();
    }

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted >= 5);
}

#[tokio::test]
async fn test_event_emitter_error_handling() {
    let emitter = EventEmitter::new_mock().await.unwrap();

    // Test with various error scenarios
    emitter.emit_service_error("").await; // Empty error
    emitter
        .emit_service_error(
            "Very long error message that should still be handled correctly without any issues",
        )
        .await;

    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted >= 2);
}
