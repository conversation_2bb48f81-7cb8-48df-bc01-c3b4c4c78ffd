//! Integration tests for the enhanced EventEmitter

use crate::whatsapp::tauri_event_emitter::{EventFilter, TauriEventType};

use super::super::events::EventEmitter;
use whatsapp_ffi_client::{ConnectionStatus, Message};

#[tokio::test]
async fn test_event_emitter_creation() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    let stats = emitter.get_stats();
    assert_eq!(stats.total_events_emitted, 0);
    assert_eq!(stats.active_subscriptions, 1); // Default subscription to All events
}

#[tokio::test]
async fn test_qr_code_emission() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Emit QR code event
    emitter.emit_qr_code("test_qr_code").await;
    
    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_message_received_emission() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Create a test message
    let message = Message::new_text(
        "test_sender".to_string(),
        "test_message".to_string(),
        1234567890,
        "msg_123".to_string(),
    );
    
    // Emit message received event
    emitter.emit_message_received(&message).await;
    
    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_connection_status_changed_emission() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Emit connection status changed event
    emitter.emit_connection_status_changed(
        ConnectionStatus::Connected,
        Some("Successfully connected".to_string())
    ).await;
    
    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_service_error_emission() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Emit service error event
    emitter.emit_service_error("Test error message").await;
    
    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
}

#[tokio::test]
async fn test_subscription_management() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Subscribe to QR code events only
    let subscription_id = emitter.subscribe(
        EventFilter::ByType(TauriEventType::QRCode)
    ).await.unwrap();
    
    assert!(!subscription_id.is_empty());
    
    let stats = emitter.get_stats();
    assert_eq!(stats.active_subscriptions, 2); // Default + new subscription
    
    // Unsubscribe
    emitter.unsubscribe(&subscription_id).await.unwrap();
    
    let stats = emitter.get_stats();
    assert_eq!(stats.active_subscriptions, 1); // Back to just default subscription
}

#[tokio::test]
async fn test_emitter_shutdown() {
    let emitter = EventEmitter::new_mock().await.unwrap();
    
    // Emit some events
    emitter.emit_qr_code("test_qr").await;
    emitter.emit_service_error("test_error").await;
    
    // Give some time for processing
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    
    let stats = emitter.get_stats();
    assert!(stats.total_events_emitted > 0);
    
    // Shutdown should not fail
    emitter.shutdown().await.unwrap();
}